import React from "react";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import {
  DashboardIcon,
  UsersIcon,
  ListingsIcon,
  TopUpRequestsIcon,
  TransactionsIcon,
  DisputesAndRefundsIcon,
  DeliveryApplicationsIcon,
  RewardsAndReferralsIcon,
  CategoriesIcon,
  PromotionsAndSponsorshipsIcon,
  PlatformSettingsIcon,
  ReportedListingIcon,
  DeliveryAgentComplaintsIcon,
} from "@/assets/svgs/AdminDashboard";

interface INavItem {
  to: string;
  text: string;
  icon: React.ReactElement;
}

const NAV_ITEMS: INavItem[] = [
  { to: "/admin/dashboard", text: "Dashboard", icon: <DashboardIcon /> },
  { to: "/admin/users", text: "Users", icon: <UsersIcon /> },
  { to: "/admin/listings", text: "Listings", icon: <ListingsIcon /> },
  {
    to: "/admin/top-up-requests",
    text: "Top-up requests",
    icon: <TopUpRequestsIcon />,
  },
  {
    to: "/admin/transactions",
    text: "Transactions",
    icon: <TransactionsIcon />,
  },
  {
    to: "/admin/disputes-refunds",
    text: "Disputes & Refunds",
    icon: <DisputesAndRefundsIcon />,
  },
  {
    to: "/admin/delivery-applications",
    text: "Delivery Applications",
    icon: <DeliveryApplicationsIcon />,
  },
  {
    to: "/admin/rewards-referrals",
    text: "Rewards & Referrals",
    icon: <RewardsAndReferralsIcon />,
  },
  { to: "/admin/categories", text: "Categories", icon: <CategoriesIcon /> },
  {
    to: "/admin/promotions-sponsorships",
    text: "Promotions & Sponsorships",
    icon: <PromotionsAndSponsorshipsIcon />,
  },
  {
    to: "/admin/platform-settings",
    text: "Platform Settings",
    icon: <PlatformSettingsIcon />,
  },
  {
    to: "/admin/reported-listing",
    text: "Reported Listing",
    icon: <ReportedListingIcon />,
  },
  {
    to: "/admin/delivery-agent-complaints",
    text: "Delivery Agent Complaints",
    icon: <DeliveryAgentComplaintsIcon />,
  },
];

const AdminHeader = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem("user");
    localStorage.removeItem("token");
    localStorage.removeItem("role");
    localStorage.removeItem("refresh_token");
    navigate("/admin/login", { replace: true });
  };

  return (
    <div className="flex h-full w-[280px] flex-col bg-[#112044] text-white">
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-500">eBaDollar</h1>
        <p className="text-sm text-gray-400">Admin Portal</p>
      </div>
      <nav className="flex-1 space-y-2 p-4">
        {NAV_ITEMS.map((item) => (
          <NavLink
            key={item.to}
            to={item.to}
            className={({ isActive }) =>
              `flex items-center space-x-3 rounded-md px-4 py-3 text-sm font-medium transition-colors ${
                isActive
                  ? "bg-red-500 text-white"
                  : "text-gray-300 hover:bg-gray-700 hover:text-white"
              }`
            }
          >
            {React.cloneElement(item.icon, { className: "h-5 w-5" })}
            <span>{item.text}</span>
          </NavLink>
        ))}
      </nav>
      <div className="p-4 mt-auto">
        <button
          onClick={handleLogout}
          className="w-full flex items-center justify-center gap-2 rounded-md bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2"
        >
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2v1" />
          </svg>
          Logout
        </button>
      </div>
    </div>
  );
};

export default AdminHeader;
