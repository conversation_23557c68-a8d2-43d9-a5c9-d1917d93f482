import { lazy } from "react";

export const AddAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AddAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ViewAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminForgotPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminLoginPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const AdminProfilePage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminSignUpPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const UserLoginPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserLoginPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDashboardPage = lazy(() => {
  const __import = import("@/pages/Admin/Dashboard/AdminDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const LandingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const UserLandingPage = lazy(() => {
  const __import = import("@/pages/User/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const UserMagicLoginPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

export const UserDashboardPage = lazy(
  () => import("../pages/User/Dashboard/Dashboard")
);

// User Marketplace Pages
export const UserMarketplaceListPage = lazy(
  () => import("../pages/User/List/UserMarketplaceListPage")
);
export const UserMarketplaceDetailPage = lazy(
  () => import("../pages/User/View/UserMarketplaceDetailPage")
);

// User Listings Pages
export const UserListingsListPage = lazy(
  () => import("../pages/User/List/UserListingsListPage")
);
export const UserAddListingPage = lazy(
  () => import("../pages/User/Add/UserAddListingPage")
);
export const UserEditListingPage = lazy(
  () => import("../pages/User/Edit/UserEditListingPage")
);
export const UserViewListingPage = lazy(
  () => import("../pages/User/View/UserViewListingPage")
);

// User Transactions Pages
export const UserTransactionsListPage = lazy(
  () => import("../pages/User/List/UserTransactionsListPage")
);
export const UserViewTransactionPage = lazy(
  () => import("../pages/User/View/UserTransactionDetailsPage")
);

// User Rewards Pages
export const UserRewardsListPage = lazy(
  () => import("../pages/User/List/UserRewardsListPage")
);

export const UserAvailableDeliveriesPage = lazy(
  () => import("../pages/User/List/UserAvailableDeliveriesPage")
);

export const UserMyDeliveriesPage = lazy(
  () => import("../pages/User/List/UserMyDeliveriesPage")
);

// User Account Pages
export const UserAccountPage = lazy(
  () => import("../pages/User/Auth/UserAccountPage")
);

export const UserDeliverySettingsPage = lazy(
  () => import("../pages/User/Auth/UserDeliverySettingsPage")
);

export const AdminAddUserPage = lazy(
  () => import("../pages/Admin/Add/AdminAddUserPage")
);

export const AdminUsersListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminUsersListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminCategoriesListPage = lazy(
  () => import("../pages/Admin/List/AdminCategoriesListPage")
);
export const AdminAddCategoryPage = lazy(
  () => import("../pages/Admin/Add/AdminAddCategoryPage")
);
export const AdminEditCategoryPage = lazy(
  () => import("../pages/Admin/Edit/AdminEditCategoryPage")
);

export const AdminListingsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListingsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminTopUpRequestsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminTopUpRequestsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminTransactionsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminTransactionsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDisputesAndRefundsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminDisputesAndRefundsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDeliveryApplicationsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminDeliveryApplicationsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminRewardsAndReferralsListPage = lazy(() => {
  const __import = import(
    "../pages/Admin/List/AdminRewardsAndReferralsListPage"
  );
  __import.finally(() => {});
  return __import;
});
export const AdminViewTransactionPage = lazy(
  () => import("../pages/Admin/View/AdminViewTransactionPage")
);

export const AdminPromotionsAndSponsorshipsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminPromotionsAndSponsorshipsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminPlatformSettingsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminPlatformSettingsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminReportedListingListPage = lazy(
  () => import("../pages/Admin/List/AdminReportedListingListPage")
);
export const AdminViewListingPage = lazy(
  () => import("../pages/Admin/View/AdminViewListingPage")
);
export const AdminDeliveryAgentComplaintsListPage = lazy(
  () => import("../pages/Admin/List/AdminDeliveryAgentComplaintsListPage")
);
export const AdminDeliveryAgentComplaintDetailsPage = lazy(
  () => import("../pages/Admin/View/AdminDeliveryAgentComplaintDetailsPage")
);

// OTHERS

export const TestComponents = lazy(() => {
  const __import = import("@/pages/PG/Custom/TestComponents");
  __import.finally(() => {});
  return __import;
});
